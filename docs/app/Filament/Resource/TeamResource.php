<?php

namespace App\Filament\Resources;

use App\Data\TeamData;
use App\Enums\TeamType;
use App\Filament\Resources\TeamResource\Pages;
use App\Models\Team;
use App\Rules\UniqueNameInGraph;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class TeamResource extends Resource
{
    protected static ?string $model = Team::class;
    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Basic Information')
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255)
                        ->live(onBlur: true)
                        ->rules([
                            fn(Forms\Get $get, ?Model $record) => new UniqueNameInGraph(
                                $record,
                                $get('parent_id')
                            )
                        ])
                        ->validationMessages([
                            'unique_name_in_graph' => 'This name is already used within the team hierarchy.',
                        ])
                        ->helperText('Name must be unique within the team hierarchy graph'),

                    Forms\Components\TextInput::make('slug')
                        ->disabled()
                        ->dehydrated(false)
                        ->helperText('Auto-generated from name and parent name')
                        ->visibleOn('edit'),

                    Forms\Components\Textarea::make('description')
                        ->maxLength(65535)
                        ->columnSpanFull(),
                ]),

            Forms\Components\Section::make('Hierarchy')
                ->schema([
                    Forms\Components\Select::make('type')
                        ->options(TeamType::class)
                        ->required()
                        ->live()
                        ->afterStateUpdated(function ($state, Forms\Set $set) {
                            if ($state === TeamType::Enterprise->value) {
                                $set('parent_id', null);
                            }
                        }),

                    Forms\Components\Select::make('parent_id')
                        ->relationship(
                            'parent',
                            'name',
                            fn(Builder $query, Forms\Get $get) => $query
                                ->when($get('type'), function ($query, $type) {
                                    $allowedParentTypes = TeamType::from($type)
                                        ->getAllowedParentTypes();
                                    if (!empty($allowedParentTypes)) {
                                        $query->whereIn('type', $allowedParentTypes);
                                    }
                                })
                        )
                        ->searchable()
                        ->preload()
                        ->live()
                        ->hidden(fn(Forms\Get $get) =>
                            $get('type') === TeamType::Enterprise->value
                        )
                        ->helperText(function (Forms\Get $get) {
                            if (!$get('type')) return null;

                            $allowedTypes = TeamType::from($get('type'))
                                ->getAllowedParentTypes();

                            return empty($allowedTypes)
                                ? 'This type cannot have a parent'
                                : 'Allowed parent types: ' .
                                  implode(', ', array_map(
                                      fn($type) => $type->getLabel(),
                                      $allowedTypes
                                  ));
                        }),
                ]),

            Forms\Components\Section::make('Leadership')
                ->schema([
                    Forms\Components\Select::make('executive_id')
                        ->relationship('executive', 'name')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live(),

                    Forms\Components\Select::make('deputy_id')
                        ->relationship('deputy', 'name')
                        ->searchable()
                        ->preload()
                        ->different('executive_id')
                        ->helperText('Deputy must be different from Executive'),
                ]),

            Forms\Components\Section::make('System Information')
                ->schema([
                    Forms\Components\TextInput::make('ulid')
                        ->label('ULID')
                        ->disabled()
                        ->dehydrated(false)
                        ->helperText('Auto-generated unique identifier'),

                    Forms\Components\Placeholder::make('graph_info')
                        ->label('Graph Information')
                        ->content(function (?Model $record) {
                            if (!$record) return 'New team';

                            $root = $record->getGraphRoot();
                            $teamsInGraph = $record->getGraphTeams()->count();

                            return $root
                                ? "Part of '{$root->name}' graph ({$teamsInGraph} teams)"
                                : "Root team ({$teamsInGraph} teams in graph)";
                        }),
                ])
                ->visibleOn('edit'),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->description(fn(Team $record) => $record->slug),

                Tables\Columns\BadgeColumn::make('type')
                    ->color(fn(Team $record) => $record->type->getColor())
                    ->formatStateUsing(fn($state) => $state->getLabel()),

                Tables\Columns\TextColumn::make('parent.name')
                    ->label('Parent')
                    ->sortable()
                    ->placeholder('Root Level')
                    ->description(function (Team $record) {
                        $root = $record->getGraphRoot();
                        return $root && $root->id !== $record->id
                            ? "Graph: {$root->name}"
                            : ($record->parent_id ? null : 'Graph Root');
                    }),

                Tables\Columns\TextColumn::make('executive.name')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('deputy.name')
                    ->sortable()
                    ->toggleable()
                    ->placeholder('Not assigned'),

                Tables\Columns\BadgeColumn::make('state')
                    ->color(fn(Team $record) => $record->state->color())
                    ->formatStateUsing(fn($state) => $state->label()),

                Tables\Columns\TextColumn::make('graph_size')
                    ->label('Graph Size')
                    ->getStateUsing(fn(Team $record) => $record->getGraphTeams()->count())
                    ->suffix(' teams')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(TeamType::class),

                Tables\Filters\SelectFilter::make('state')
                    ->options([
                        'draft' => 'Draft',
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'archived' => 'Archived',
                    ]),

                Tables\Filters\Filter::make('root_only')
                    ->label('Root Teams Only')
                    ->query(fn(Builder $query) => $query->whereNull('parent_id'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('view_graph')
                    ->label('View Graph')
                    ->icon('heroicon-o-share')
                    ->url(fn(Team $record) => route('teams.hierarchy', $record->ulid))
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make(),
            ])
            ->defaultSort('created_at', 'desc')
            ->groups([
                Tables\Grouping\Group::make('parent.name')
                    ->label('Parent Teams')
                    ->collapsible(),

                Tables\Grouping\Group::make('type')
                    ->label('Teams Type')
                    ->collapsible(),
            ]);
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()
            ->with(['parent', 'executive', 'deputy']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'slug', 'ulid', 'description'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        $root = $record->getGraphRoot();

        return [
            'Type' => $record->type->getLabel(),
            'Parent' => $record->parent?->name ?? 'Root Level',
            'Graph' => $root?->name ?? 'Independent',
            'Executive' => $record->executive->name,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeams::route('/'),
            'create' => Pages\CreateTeam::route('/create'),
            'edit' => Pages\EditTeam::route('/{record:ulid}/edit'),
            'view' => Pages\ViewTeam::route('/{record:ulid}'),
        ];
    }
}
