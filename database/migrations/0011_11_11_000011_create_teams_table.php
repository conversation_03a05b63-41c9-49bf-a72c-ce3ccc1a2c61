<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('teams', function (Blueprint $table) {
            $table->id();
            $table->string('ulid', 26)->unique();
            $table->string('name');
            $table->string('slug', 150)->unique();
            $table->text('description')->nullable();
            $table->string('type');
            $table->foreignId('parent_id')->nullable()->constrained('teams');
            $table->foreignId('executive_id')->constrained('users');
            $table->foreignId('deputy_id')->nullable()->constrained('users');
            $table->string('state')->default('draft');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['type', 'parent_id']);
            $table->index('state');
            $table->index('ulid');
            $table->index('slug');
            $table->index(['name', 'parent_id']);
        });

        // Add a partial unique constraint for root teams (where parent_id is null)
        DB::statement('CREATE UNIQUE INDEX teams_name_root_unique ON teams (name) WHERE parent_id IS NULL');
    }

    public function down(): void
    {
        Schema::dropIfExists('teams');
    }
};
