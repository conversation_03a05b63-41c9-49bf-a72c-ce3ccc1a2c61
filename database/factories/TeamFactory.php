<?php

namespace Database\Factories;

use App\Enums\TeamType;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Symfony\Component\Uid\Ulid;

class TeamFactory extends Factory
{
    protected $model = Team::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->company(),
            'ulid' => (string) new Ulid(),
            'description' => $this->faker->paragraph(),
            'type' => $this->faker->randomElement(TeamType::cases()),
            'executive_id' => User::factory(),
            'deputy_id' => $this->faker->boolean(70) ? User::factory() : null,
        ];
    }

    public function withUniqueNameInGraph(?Team $parent = null): static
    {
        return $this->state(function (array $attributes) use ($parent) {
            $baseName = $this->faker->company();
            $name = $baseName;
            $counter = 1;

            // Ensure uniqueness within the graph
            while (!$this->isNameUniqueInGraph($name, $parent)) {
                $name = "{$baseName} {$counter}";
                $counter++;
            }

            return ['name' => $name];
        });
    }

    private function isNameUniqueInGraph(string $name, ?Team $parent): bool
    {
        $query = Team::where('name', $name);

        if ($parent) {
            $rootId = $parent->getGraphRoot()?->id;
            if ($rootId) {
                $query->where(function ($query) use ($rootId) {
                    $query->whereHas('ancestors', function ($ancestorQuery) use ($rootId) {
                        $ancestorQuery->where('id', $rootId);
                    })->orWhere('id', $rootId);
                });
            }
        } else {
            $query->whereNull('parent_id');
        }

        return !$query->exists();
    }

    public function enterprise(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => TeamType::Enterprise,
            'parent_id' => null,
        ]);
    }

    public function organisation(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => TeamType::Organisation,
        ]);
    }

    public function division(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => TeamType::Division,
        ]);
    }

    public function department(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => TeamType::Department,
        ]);
    }

    public function project(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => TeamType::Project,
        ]);
    }
}
