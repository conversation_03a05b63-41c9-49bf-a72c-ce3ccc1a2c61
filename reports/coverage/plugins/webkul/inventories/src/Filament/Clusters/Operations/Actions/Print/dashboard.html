<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/aureuserp/plugins/webkul/inventories/src/Filament/Clusters/Operations/Actions/Print</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../../../../../_css/bootstrap.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../../_css/nv.d3.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../../_css/style.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../../../../../index.html">/Users/<USER>/Herd/aureuserp</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../../../index.html">plugins</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../../index.html">webkul</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../index.html">inventories</a></li>
         <li class="breadcrumb-item"><a href="../../../../../index.html">src</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Filament</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Clusters</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Operations</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Print</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DeliverySlipAction.php.html#10">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\DeliverySlipAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LabelsAction.php.html#19">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\LabelsAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PackageAction.php.html#10">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\PackageAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PickingOperationAction.php.html#10">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\PickingOperationAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReturnSlipAction.php.html#13">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\ReturnSlipAction</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LabelsAction.php.html#19">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\LabelsAction</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ReturnSlipAction.php.html#13">Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\ReturnSlipAction</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DeliverySlipAction.php.html#12"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\DeliverySlipAction::setUp">setUp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeliverySlipAction.php.html#31"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\DeliverySlipAction::getDefaultName">getDefaultName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LabelsAction.php.html#21"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\LabelsAction::setUp">setUp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LabelsAction.php.html#135"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\LabelsAction::getDefaultName">getDefaultName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PackageAction.php.html#12"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\PackageAction::setUp">setUp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PackageAction.php.html#33"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\PackageAction::getDefaultName">getDefaultName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PickingOperationAction.php.html#12"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\PickingOperationAction::setUp">setUp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PickingOperationAction.php.html#31"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\PickingOperationAction::getDefaultName">getDefaultName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReturnSlipAction.php.html#15"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\ReturnSlipAction::setUp">setUp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReturnSlipAction.php.html#46"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\ReturnSlipAction::getDefaultName">getDefaultName</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LabelsAction.php.html#21"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\LabelsAction::setUp">setUp</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ReturnSlipAction.php.html#15"><abbr title="Webkul\Inventory\Filament\Clusters\Operations\Actions\Print\ReturnSlipAction::setUp">setUp</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.9</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.8</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Wed Jun 18 21:32:18 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../../../../../_js/jquery.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../../../../../../../../../_js/d3.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../../../../../../../../../_js/nv.d3.min.js?v=11.0.9" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([10,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"DeliverySlipAction.php.html#10\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\DeliverySlipAction<\/a>"],[0,6,"<a href=\"LabelsAction.php.html#19\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\LabelsAction<\/a>"],[0,2,"<a href=\"PackageAction.php.html#10\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\PackageAction<\/a>"],[0,2,"<a href=\"PickingOperationAction.php.html#10\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\PickingOperationAction<\/a>"],[0,3,"<a href=\"ReturnSlipAction.php.html#13\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\ReturnSlipAction<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"DeliverySlipAction.php.html#12\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\DeliverySlipAction::setUp<\/a>"],[0,1,"<a href=\"DeliverySlipAction.php.html#31\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\DeliverySlipAction::getDefaultName<\/a>"],[0,5,"<a href=\"LabelsAction.php.html#21\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\LabelsAction::setUp<\/a>"],[0,1,"<a href=\"LabelsAction.php.html#135\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\LabelsAction::getDefaultName<\/a>"],[0,1,"<a href=\"PackageAction.php.html#12\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\PackageAction::setUp<\/a>"],[0,1,"<a href=\"PackageAction.php.html#33\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\PackageAction::getDefaultName<\/a>"],[0,1,"<a href=\"PickingOperationAction.php.html#12\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\PickingOperationAction::setUp<\/a>"],[0,1,"<a href=\"PickingOperationAction.php.html#31\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\PickingOperationAction::getDefaultName<\/a>"],[0,2,"<a href=\"ReturnSlipAction.php.html#15\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\ReturnSlipAction::setUp<\/a>"],[0,1,"<a href=\"ReturnSlipAction.php.html#46\">Webkul\\Inventory\\Filament\\Clusters\\Operations\\Actions\\Print\\ReturnSlipAction::getDefaultName<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
