<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /Users/<USER>/Herd/aureuserp/plugins/webkul/invoices/resources/lang/en/enums/auto-post-bills.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../../../_css/bootstrap.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../_css/octicons.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../_css/style.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../../../index.html">/Users/<USER>/Herd/aureuserp</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../index.html">plugins</a></li>
         <li class="breadcrumb-item"><a href="../../../../../index.html">webkul</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">invoices</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">resources</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">lang</a></li>
         <li class="breadcrumb-item"><a href="../index.html">en</a></li>
         <li class="breadcrumb-item"><a href="index.html">enums</a></li>
         <li class="breadcrumb-item active">auto-post-bills.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;5</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">declare</span><span class="keyword">(</span><span class="default">strict_types</span><span class="keyword">=</span><span class="default">1</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'always'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Always'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'ask'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Ask&nbsp;after&nbsp;3&nbsp;validations&nbsp;without&nbsp;edits'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'never'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Never'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">]</span><span class="keyword">;</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.9</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.8</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Wed Jun 18 21:14:41 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../../../../../_js/jquery.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../../../../../../../_js/bootstrap.bundle.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../../../../../../../_js/file.js?v=11.0.9" type="text/javascript"></script>
 </body>
</html>
