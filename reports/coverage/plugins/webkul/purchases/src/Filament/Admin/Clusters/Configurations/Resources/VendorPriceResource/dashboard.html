<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/aureuserp/plugins/webkul/purchases/src/Filament/Admin/Clusters/Configurations/Resources/VendorPriceResource</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../../../../../../_css/bootstrap.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../../../_css/nv.d3.min.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../../../_css/style.css?v=11.0.10" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../../../../../../index.html">/Users/<USER>/Herd/aureuserp</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../../../../index.html">plugins</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../../../index.html">webkul</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../../index.html">purchases</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../index.html">src</a></li>
         <li class="breadcrumb-item"><a href="../../../../../index.html">Filament</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Admin</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Clusters</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Configurations</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Resources</a></li>
         <li class="breadcrumb-item"><a href="index.html">VendorPriceResource</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Pages/CreateVendorPrice.php.html#13">Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\CreateVendorPrice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/EditVendorPrice.php.html#16">Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\EditVendorPrice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/ListVendorPrices.php.html#14">Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\ListVendorPrices</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/ViewVendorPrice.php.html#12">Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\ViewVendorPrice</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Pages/EditVendorPrice.php.html#16">Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\EditVendorPrice</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Pages/CreateVendorPrice.php.html#17"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\CreateVendorPrice::getTitle">getTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/CreateVendorPrice.php.html#22"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\CreateVendorPrice::getRedirectUrl">getRedirectUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/CreateVendorPrice.php.html#27"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\CreateVendorPrice::getCreatedNotification">getCreatedNotification</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/CreateVendorPrice.php.html#35"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\CreateVendorPrice::mutateFormDataBeforeCreate">mutateFormDataBeforeCreate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/EditVendorPrice.php.html#20"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\EditVendorPrice::getTitle">getTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/EditVendorPrice.php.html#25"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\EditVendorPrice::getSavedNotification">getSavedNotification</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/EditVendorPrice.php.html#33"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\EditVendorPrice::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/ListVendorPrices.php.html#18"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\ListVendorPrices::getTitle">getTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/ListVendorPrices.php.html#23"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\ListVendorPrices::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/ViewVendorPrice.php.html#16"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\ViewVendorPrice::getTitle">getTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pages/ViewVendorPrice.php.html#21"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\ViewVendorPrice::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Pages/EditVendorPrice.php.html#33"><abbr title="Webkul\Purchase\Filament\Admin\Clusters\Configurations\Resources\VendorPriceResource\Pages\EditVendorPrice::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.10</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.8</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Wed Jun 18 22:48:54 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../../../../../../_js/jquery.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../../../../../../../../../../_js/d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script src="../../../../../../../../../../_js/nv.d3.min.js?v=11.0.10" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([4,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"Pages\/CreateVendorPrice.php.html#13\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\CreateVendorPrice<\/a>"],[0,4,"<a href=\"Pages\/EditVendorPrice.php.html#16\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\EditVendorPrice<\/a>"],[0,2,"<a href=\"Pages\/ListVendorPrices.php.html#14\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\ListVendorPrices<\/a>"],[0,2,"<a href=\"Pages\/ViewVendorPrice.php.html#12\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\ViewVendorPrice<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Pages\/CreateVendorPrice.php.html#17\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\CreateVendorPrice::getTitle<\/a>"],[0,1,"<a href=\"Pages\/CreateVendorPrice.php.html#22\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\CreateVendorPrice::getRedirectUrl<\/a>"],[0,1,"<a href=\"Pages\/CreateVendorPrice.php.html#27\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\CreateVendorPrice::getCreatedNotification<\/a>"],[0,1,"<a href=\"Pages\/CreateVendorPrice.php.html#35\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\CreateVendorPrice::mutateFormDataBeforeCreate<\/a>"],[0,1,"<a href=\"Pages\/EditVendorPrice.php.html#20\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\EditVendorPrice::getTitle<\/a>"],[0,1,"<a href=\"Pages\/EditVendorPrice.php.html#25\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\EditVendorPrice::getSavedNotification<\/a>"],[0,2,"<a href=\"Pages\/EditVendorPrice.php.html#33\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\EditVendorPrice::getHeaderActions<\/a>"],[0,1,"<a href=\"Pages\/ListVendorPrices.php.html#18\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\ListVendorPrices::getTitle<\/a>"],[0,1,"<a href=\"Pages\/ListVendorPrices.php.html#23\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\ListVendorPrices::getHeaderActions<\/a>"],[0,1,"<a href=\"Pages\/ViewVendorPrice.php.html#16\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\ViewVendorPrice::getTitle<\/a>"],[0,1,"<a href=\"Pages\/ViewVendorPrice.php.html#21\">Webkul\\Purchase\\Filament\\Admin\\Clusters\\Configurations\\Resources\\VendorPriceResource\\Pages\\ViewVendorPrice::getHeaderActions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
