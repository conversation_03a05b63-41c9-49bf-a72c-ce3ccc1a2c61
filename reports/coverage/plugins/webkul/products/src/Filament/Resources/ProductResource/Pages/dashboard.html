<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Herd/aureuserp/plugins/webkul/products/src/Filament/Resources/ProductResource/Pages</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../../../../_css/bootstrap.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../_css/nv.d3.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../_css/style.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../../../../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../../../../index.html">/Users/<USER>/Herd/aureuserp</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../../index.html">plugins</a></li>
         <li class="breadcrumb-item"><a href="../../../../../../index.html">webkul</a></li>
         <li class="breadcrumb-item"><a href="../../../../../index.html">products</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">src</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Filament</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Resources</a></li>
         <li class="breadcrumb-item"><a href="../index.html">ProductResource</a></li>
         <li class="breadcrumb-item"><a href="index.html">Pages</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateProduct.php.html#12">Webkul\Product\Filament\Resources\ProductResource\Pages\CreateProduct</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EditProduct.php.html#18">Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListProducts.php.html#16">Webkul\Product\Filament\Resources\ProductResource\Pages\ListProducts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageAttributes.php.html#27">Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageVariants.php.html#17">Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewProduct.php.html#18">Webkul\Product\Filament\Resources\ProductResource\Pages\ViewProduct</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ManageVariants.php.html#17">Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="EditProduct.php.html#18">Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ManageAttributes.php.html#27">Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ViewProduct.php.html#18">Webkul\Product\Filament\Resources\ProductResource\Pages\ViewProduct</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateProduct.php.html#16"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\CreateProduct::getRedirectUrl">getRedirectUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateProduct.php.html#21"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\CreateProduct::getCreatedNotification">getCreatedNotification</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateProduct.php.html#29"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\CreateProduct::mutateFormDataBeforeCreate">mutateFormDataBeforeCreate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EditProduct.php.html#22"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct::getSubNavigationPosition">getSubNavigationPosition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EditProduct.php.html#27"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct::getRedirectUrl">getRedirectUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EditProduct.php.html#32"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct::getSavedNotification">getSavedNotification</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EditProduct.php.html#40"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EditProduct.php.html#96"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct::afterSave">afterSave</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListProducts.php.html#22"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ListProducts::table">table</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListProducts.php.html#30"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ListProducts::getPresetTableViews">getPresetTableViews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListProducts.php.html#56"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ListProducts::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageAttributes.php.html#35"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes::getSubNavigationPosition">getSubNavigationPosition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageAttributes.php.html#40"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes::getNavigationLabel">getNavigationLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageAttributes.php.html#45"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes::form">form</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageAttributes.php.html#81"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes::table">table</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageAttributes.php.html#138"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes::updateOrCreateVariants">updateOrCreateVariants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageVariants.php.html#25"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants::getSubNavigationPosition">getSubNavigationPosition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageVariants.php.html#30"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants::getNavigationLabel">getNavigationLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageVariants.php.html#35"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants::form">form</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageVariants.php.html#40"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants::table">table</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ManageVariants.php.html#79"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants::infolist">infolist</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewProduct.php.html#22"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ViewProduct::getSubNavigationPosition">getSubNavigationPosition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewProduct.php.html#27"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ViewProduct::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ManageVariants.php.html#40"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageVariants::table">table</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="EditProduct.php.html#40"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\EditProduct::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ViewProduct.php.html#27"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ViewProduct::getHeaderActions">getHeaderActions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ManageAttributes.php.html#45"><abbr title="Webkul\Product\Filament\Resources\ProductResource\Pages\ManageAttributes::form">form</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.9</a> using <a href="https://www.php.net/" target="_top">PHP 8.4.8</a> and <a href="https://phpunit.de/">PHPUnit 11.5.15</a> at Wed Jun 18 20:23:03 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../../../../_js/jquery.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../../../../../../../../_js/d3.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../../../../../../../../_js/nv.d3.min.js?v=11.0.9" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([23,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"CreateProduct.php.html#12\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\CreateProduct<\/a>"],[0,7,"<a href=\"EditProduct.php.html#18\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\EditProduct<\/a>"],[0,3,"<a href=\"ListProducts.php.html#16\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ListProducts<\/a>"],[0,6,"<a href=\"ManageAttributes.php.html#27\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageAttributes<\/a>"],[0,9,"<a href=\"ManageVariants.php.html#17\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageVariants<\/a>"],[0,4,"<a href=\"ViewProduct.php.html#18\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ViewProduct<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"CreateProduct.php.html#16\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\CreateProduct::getRedirectUrl<\/a>"],[0,1,"<a href=\"CreateProduct.php.html#21\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\CreateProduct::getCreatedNotification<\/a>"],[0,1,"<a href=\"CreateProduct.php.html#29\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\CreateProduct::mutateFormDataBeforeCreate<\/a>"],[0,1,"<a href=\"EditProduct.php.html#22\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\EditProduct::getSubNavigationPosition<\/a>"],[0,1,"<a href=\"EditProduct.php.html#27\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\EditProduct::getRedirectUrl<\/a>"],[0,1,"<a href=\"EditProduct.php.html#32\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\EditProduct::getSavedNotification<\/a>"],[0,3,"<a href=\"EditProduct.php.html#40\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\EditProduct::getHeaderActions<\/a>"],[0,1,"<a href=\"EditProduct.php.html#96\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\EditProduct::afterSave<\/a>"],[0,1,"<a href=\"ListProducts.php.html#22\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ListProducts::table<\/a>"],[0,1,"<a href=\"ListProducts.php.html#30\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ListProducts::getPresetTableViews<\/a>"],[0,1,"<a href=\"ListProducts.php.html#56\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ListProducts::getHeaderActions<\/a>"],[0,1,"<a href=\"ManageAttributes.php.html#35\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageAttributes::getSubNavigationPosition<\/a>"],[0,1,"<a href=\"ManageAttributes.php.html#40\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageAttributes::getNavigationLabel<\/a>"],[0,2,"<a href=\"ManageAttributes.php.html#45\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageAttributes::form<\/a>"],[0,1,"<a href=\"ManageAttributes.php.html#81\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageAttributes::table<\/a>"],[0,1,"<a href=\"ManageAttributes.php.html#138\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageAttributes::updateOrCreateVariants<\/a>"],[0,1,"<a href=\"ManageVariants.php.html#25\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageVariants::getSubNavigationPosition<\/a>"],[0,1,"<a href=\"ManageVariants.php.html#30\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageVariants::getNavigationLabel<\/a>"],[0,1,"<a href=\"ManageVariants.php.html#35\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageVariants::form<\/a>"],[0,5,"<a href=\"ManageVariants.php.html#40\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageVariants::table<\/a>"],[0,1,"<a href=\"ManageVariants.php.html#79\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ManageVariants::infolist<\/a>"],[0,1,"<a href=\"ViewProduct.php.html#22\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ViewProduct::getSubNavigationPosition<\/a>"],[0,3,"<a href=\"ViewProduct.php.html#27\">Webkul\\Product\\Filament\\Resources\\ProductResource\\Pages\\ViewProduct::getHeaderActions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
