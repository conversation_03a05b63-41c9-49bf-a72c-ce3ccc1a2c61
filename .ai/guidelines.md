
# AureusERP AI Assistant Project Guidelines

> **Note:** The project guidelines have been restructured into a more organized format.
> Please refer to the new guidelines in the `.ai/guidelines/` directory.

## Core Communication Principle

**All documents and responses should be clear, actionable, and suitable for a junior developer to understand and implement.**

## Guidelines Structure

The guidelines are now organized into the following files:

1. [Main Index](guidelines/000-index.md) - Overview and navigation of all guidelines
2. [Project Overview](guidelines/010-project-overview.md) - Core information about AureusERP
3. [Documentation Standards](guidelines/020-documentation-standards.md) - Guidelines for documentation
4. [Development Standards](guidelines/030-development-standards.md) - Code style and architecture patterns
5. [Workflow Guidelines](guidelines/040-workflow-guidelines.md) - Git workflow and terminal management
6. [Plugin Architecture](guidelines/050-plugin-architecture.md) - Working with plugins

## Purpose

These guidelines serve two main purposes:

1. To provide comprehensive information about the AureusERP project structure, architecture, and development standards
2. To establish consistent formatting, behavior, and workflow standards for the AI Assistant when working with the codebase

By following these guidelines, you'll ensure that your contributions to AureusERP maintain the project's high standards for code quality, performance, and user experience.

## Maintenance

These guidelines should be updated whenever there are significant changes to:

- Project architecture or structure
- Development standards or workflows
- Documentation requirements
- AI Assistant behavior or capabilities

When updating these guidelines, ensure that all affected documents are updated consistently and that the main index reflects the current structure.
