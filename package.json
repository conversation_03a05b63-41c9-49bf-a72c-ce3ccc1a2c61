{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "concurrently": "^9.1.2", "laravel-vite-plugin": "^1.3.0", "tailwindcss": "^4.1.10", "vite": "^6.3.5"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.0", "@tailwindcss/oxide-linux-x64-gnu": "^4.1.10", "lightningcss-linux-x64-gnu": "^1.30.1"}, "devEngines": {"packageManager": {"name": "pnpm", "onFail": "warn"}, "runtime": {"name": "node", "onFail": "error"}}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.0.0"}, "packageManager": "pnpm@10.12.1"}