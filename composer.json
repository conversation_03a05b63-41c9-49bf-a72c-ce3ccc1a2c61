{"$schema": "https://getcomposer.org/schema.json", "name": "s-a-c/fm4", "type": "project", "description": "The official Laravel starter kit for Livewire.", "keywords": ["laravel", "framework"], "license": "MIT", "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "laravel-comments": {"type": "composer", "url": "https://satis.spatie.be"}}, "require": {"php": "^8.4", "filament/filament": "^4.0", "laravel/framework": "^12.0", "laravel/tinker": "^2.10.1", "livewire/flux": "^2.1", "livewire/flux-pro": "^2.2", "livewire/volt": "^1.7.0", "spatie/laravel-data": "^4.15", "spatie/laravel-model-states": "^2.11", "spatie/laravel-model-status": "^1.18", "spatie/laravel-sluggable": "^3.7", "staudenmeir/eloquent-param-limit-fix-x-laravel-adjacency-list": "^1.3", "tightenco/parental": "^1.4"}, "require-dev": {"alebatistella/duskapiconf": "^1.2", "driftingly/rector-laravel": "^2.0", "ergebnis/composer-normalize": "^2.47", "fakerphp/faker": "^1.24", "filament/upgrade": "^4.0", "infection/infection": "^0.29.14", "jasonmccreary/laravel-test-assertions": "^2.8", "larastan/larastan": "^3.4", "laravel-shift/blueprint": "^2.12", "laravel/dusk": "^8.3", "laravel/pail": "^1.2", "laravel/pint": "^1.22", "laravel/sail": "^1.43", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.8", "nunomaduro/phpinsights": "^2.13", "peckphp/peck": "^0.1", "pestphp/pest": "^3.8", "pestphp/pest-plugin": "^3.x-dev", "pestphp/pest-plugin-arch": "^3.1", "pestphp/pest-plugin-faker": "^3.0", "pestphp/pest-plugin-laravel": "^3.2", "pestphp/pest-plugin-livewire": "^3.0", "pestphp/pest-plugin-stressless": "^3.1", "pestphp/pest-plugin-type-coverage": "^3.5", "php-parallel-lint/php-parallel-lint": "^1.4", "rector/rector": "^2.0", "rector/type-perfect": "^2.0", "roave/security-advisories": "dev-latest", "soloterm/solo": "^0.5", "spatie/laravel-blade-comments": "^1.4", "spatie/laravel-horizon-watcher": "^1.1", "spatie/laravel-ray": "^1.40", "spatie/laravel-web-tinker": "^1.10", "spatie/pest-plugin-snapshots": "^2.2", "symfony/polyfill-php84": "^1.31"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "pnpm run concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"pnpm run dev\" --names=server,queue,logs,vite --kill-others"], "test": ["@clear", "pest"], "test:coverage": "pest --coverage", "test:coverage-html": "pest --coverage --coverage-html=reports/coverage", "test:parallel": "pest --parallel", "test:type-coverage": "pest --type-coverage", "test:arch": "pest --group=arch", "test:stress": "pest --group=stress", "test:unit": "pest --group=unit", "test:feature": "pest --group=feature", "test:integration": "pest --group=integration", "test:database": "pest --group=database", "test:api": "pest --group=api", "test:ui": "pest --group=ui", "test:performance": "pest --group=performance", "test:security": "pest --group=security", "test:validation": "pest --group=validation", "test:error-handling": "pest --group=error-handling", "pint": "pint", "pint:test": "pint --test", "phpstan": "phpstan analyse", "rector": "rector process", "rector:dry-run": "rector process --dry-run", "insights": "phpinsights", "analyze": ["@pint:test", "@phpstan", "@rector:dry-run", "@insights"], "fix": ["@pint", "@rector"], "clear": ["@php artisan cache:clear --ansi", "@php artisan config:clear --ansi", "@php artisan route:clear --ansi", "@php artisan view:clear --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "infection/extension-installer": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}