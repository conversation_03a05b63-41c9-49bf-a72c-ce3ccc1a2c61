<?php

namespace App\Data;

use App\Enums\TeamType;
use App\Models\User;
use <PERSON><PERSON>\LaravelData\Data;

class TeamData extends Data
{
    public function __construct(
        public string $name,
        public ?string $description,
        public TeamType $type,
        public ?int $parent_id,
        public int $executive_id,
        public ?int $deputy_id,
    ) {}

    public static function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'type' => ['required', 'enum:' . TeamType::class],
            'parent_id' => ['nullable', 'exists:teams,id'],
            'executive_id' => ['required', 'exists:users,id'],
            'deputy_id' => ['nullable', 'exists:users,id', 'different:executive_id'],
        ];
    }
}
