<?php

namespace App\Rules;

use App\Models\Team;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueNameInGraph implements ValidationRule
{
    public function __construct(
        private ?Team $team = null,
        private ?int $parentId = null
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            return;
        }

        $query = Team::where('name', $value);

        // Exclude current team if updating
        if ($this->team) {
            $query->where('id', '!=', $this->team->id);
        }

        // Determine the root of the graph
        $rootId = null;
        if ($this->parentId) {
            $parent = Team::find($this->parentId);
            $rootId = $parent?->getGraphRoot()?->id;
        } elseif ($this->team && $this->team->parent_id) {
            $rootId = $this->team->getGraphRoot()?->id;
        }

        // Apply graph constraint
        $query->where(function ($query) use ($rootId) {
            if ($rootId) {
                // Check within the same graph
                $query->whereHas('ancestors', function ($ancestorQuery) use ($rootId) {
                    $ancestorQuery->where('id', $rootId);
                })->orWhere('id', $rootId);
            } else {
                // Check only root teams
                $query->whereNull('parent_id');
            }
        });

        if ($query->exists()) {
            $fail('The :attribute must be unique within the team hierarchy graph.');
        }
    }
}
