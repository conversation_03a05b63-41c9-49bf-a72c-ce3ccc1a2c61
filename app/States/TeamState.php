<?php

namespace App\States;

use <PERSON><PERSON>\ModelStates\State;
use Spa<PERSON>\ModelStates\StateConfig;

abstract class TeamState extends State
{
    abstract public function color(): string;
    abstract public function label(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Draft::class)
            ->allowTransition(Draft::class, Active::class)
            ->allowTransition(Active::class, Inactive::class)
            ->allowTransition(Inactive::class, Active::class)
            ->allowTransition([Draft::class, Inactive::class], Archived::class);
    }
}
