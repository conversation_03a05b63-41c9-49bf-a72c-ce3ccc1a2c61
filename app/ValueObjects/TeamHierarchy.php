<?php

namespace App\ValueObjects;

use App\Enums\TeamType;
use App\Models\Team;
use InvalidArgumentException;

class TeamHierarchy
{
    public function __construct(private Team $team) {}

    public function validate(): void
    {
        $this->validateRootConstraint();
        $this->validateParentTypeConstraint();
        $this->validateCircularReference();
    }

    private function validateRootConstraint(): void
    {
        if (!$this->team->parent_id && !$this->team->type->canBeRoot()) {
            throw new InvalidArgumentException(
                "Only Enterprise teams can be root-level. {$this->team->type->getLabel()} must have a parent."
            );
        }
    }

    private function validateParentTypeConstraint(): void
    {
        if (!$this->team->parent_id) {
            return;
        }

        $parent = Team::find($this->team->parent_id);
        if (!$parent) {
            throw new InvalidArgumentException('Parent team does not exist.');
        }

        $allowedParentTypes = $this->team->type->getAllowedParentTypes();
        if (!in_array($parent->type, $allowedParentTypes)) {
            throw new InvalidArgumentException(
                "Invalid parent type. {$this->team->type->getLabel()} can only have parents of type: " .
                implode(', ', array_map(fn($type) => $type->getLabel(), $allowedParentTypes))
            );
        }
    }

    private function validateCircularReference(): void
    {
        if (!$this->team->parent_id) {
            return;
        }

        $ancestors = $this->team->ancestors()->pluck('id')->toArray();
        if (in_array($this->team->id, $ancestors)) {
            throw new InvalidArgumentException(
                'Circular reference detected in team hierarchy.'
            );
        }
    }

    public function getDepth(): int
    {
        return $this->team->ancestors()->count();
    }

    public function getPath(): array
    {
        return $this->team->ancestorsAndSelf()
            ->orderBy('depth')
            ->pluck('name')
            ->toArray();
    }
}
