<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum TeamType: string implements HasLabel, HasColor
{
    case Enterprise = 'enterprise';
    case Organisation = 'organisation';
    case Division = 'division';
    case Department = 'department';
    case Project = 'project';

    public function getLabel(): string
    {
        return match ($this) {
            self::Enterprise => 'Enterprise',
            self::Organisation => 'Organisation',
            self::Division => 'Division',
            self::Department => 'Department',
            self::Project => 'Project',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::Enterprise => 'purple',
            self::Organisation => 'blue',
            self::Division => 'green',
            self::Department => 'orange',
            self::Project => 'gray',
        };
    }

    public function canBeRoot(): bool
    {
        return $this === self::Enterprise;
    }

    public function getAllowedParentTypes(): array
    {
        return match ($this) {
            self::Enterprise => [],
            self::Organisation => [self::Enterprise],
            self::Division => [self::Organisation, self::Division],
            self::Department => [self::Division, self::Department],
            self::Project => [
                self::Enterprise,
                self::Organisation,
                self::Division,
                self::Department,
            ],
        };
    }
}
