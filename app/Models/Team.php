<?php

namespace App\Models;

use App\Enums\TeamType;
use App\States\Teams\Teams\Teams\Teams\Teams\TeamState;
use App\ValueObjects\TeamHierarchy;
use Filament\Support\Colors\Color;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Validation\ValidationException;
use Spatie\LaravelData\WithData;
use Spatie\ModelStates\HasStates;
use Spatie\ModelStatus\HasStatuses;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;
use Symfony\Component\Uid\Ulid;
use Tightenco\Parental\HasParent;

class Team extends Model
{
    use HasRecursiveRelationships,
        HasStates,
        HasStatuses,
        HasSlug,
        SoftDeletes,
        WithData;

    protected $fillable = [
        'name',
        'slug',
        'ulid',
        'description',
        'type',
        'parent_id',
        'executive_id',
        'deputy_id',
    ];

    protected $casts = [
        'type' => TeamType::class,
        'state' => TeamState::class,
    ];

    protected static function booted(): void
    {
        static::creating(function (Team $team) {
            if (!$team->ulid) {
                $team->ulid = (string) new Ulid();
            }
            $team->validateUniqueNameInGraph();
            $team->validateHierarchyRules();
        });

        static::updating(function (Team $team) {
            if ($team->isDirty('name') || $team->isDirty('parent_id')) {
                $team->validateUniqueNameInGraph();
            }
            $team->validateHierarchyRules();
            $team->validateExecutiveDeputyConstraint();
        });

        static::saved(function (Team $team) {
            // Regenerate slugs for descendants when parent name changes
            if ($team->isDirty('name')) {
                $team->updateDescendantSlugs();
            }
        });
    }

    // Slug Configuration
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('generateSlugSource')
            ->saveSlugsTo('slug')
            ->slugsShouldBeNoLongerThan(150)
            ->usingSeparator('-')
            ->usingLanguage('en');
    }

    public function generateSlugSource(): string
    {
        $parentName = $this->parent?->name ?? '';

        return $parentName
            ? "{$parentName} {$this->name}"
            : $this->name;
    }

    // Route Key Name for URL binding
    public function getRouteKeyName(): string
    {
        return 'ulid';
    }

    // Alternative route binding by slug
    public function resolveRouteBinding($value, $field = null)
    {
        return match ($field) {
            'slug' => $this->where('slug', $value)->first(),
            'ulid' => $this->where('ulid', $value)->first(),
            default => parent::resolveRouteBinding($value, $field),
        };
    }

    // Relationships
    public function executive(): BelongsTo
    {
        return $this->belongsTo(User::class, 'executive_id');
    }

    public function deputy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deputy_id');
    }

    // Validation Methods
    protected function validateUniqueNameInGraph(): void
    {
        $rootId = $this->getRootId();

        $query = static::query()
            ->where('name', $this->name)
            ->where(function ($query) use ($rootId) {
                if ($rootId) {
                    // For non-root teams, check within the same graph
                    $query->whereHas('ancestors', function ($ancestorQuery) use ($rootId) {
                        $ancestorQuery->where('id', $rootId);
                    })->orWhere('id', $rootId);
                } else {
                    // For root teams, check only other root teams
                    $query->whereNull('parent_id');
                }
            });

        if ($this->exists) {
            $query->where('id', '!=', $this->id);
        }

        if ($query->exists()) {
            throw ValidationException::withMessages([
                'name' => ['The name must be unique within the team hierarchy graph.']
            ]);
        }
    }

    protected function getRootId(): ?int
    {
        if (!$this->parent_id) {
            return null; // This is a root team
        }

        // Get the root ancestor
        $root = $this->ancestors()->whereNull('parent_id')->first();
        return $root?->id;
    }

    protected function validateHierarchyRules(): void
    {
        $hierarchy = new TeamHierarchy($this);
        $hierarchy->validate();
    }

    protected function validateExecutiveDeputyConstraint(): void
    {
        if ($this->executive_id && $this->deputy_id &&
            $this->executive_id === $this->deputy_id) {
            throw new \InvalidArgumentException(
                'Executive and Deputy cannot be the same person'
            );
        }
    }

    protected function updateDescendantSlugs(): void
    {
        $this->descendants()->each(function (Team $descendant) {
            $descendant->generateSlug();
            $descendant->saveQuietly(); // Avoid triggering events
        });
    }

    // Helper Methods
    public function getGraphRoot(): ?Team
    {
        return $this->ancestors()->whereNull('parent_id')->first() ??
               ($this->parent_id ? null : $this);
    }

    public function getGraphTeams(): \Illuminate\Database\Eloquent\Collection
    {
        $root = $this->getGraphRoot();

        if (!$root) {
            return collect([$this]);
        }

        return $root->descendantsAndSelf();
    }

    public function isNameUniqueInGraph(string $name, ?int $excludeId = null): bool
    {
        $rootId = $this->getRootId();

        $query = static::query()
            ->where('name', $name)
            ->where(function ($query) use ($rootId) {
                if ($rootId) {
                    $query->whereHas('ancestors', function ($ancestorQuery) use ($rootId) {
                        $ancestorQuery->where('id', $rootId);
                    })->orWhere('id', $rootId);
                } else {
                    $query->whereNull('parent_id');
                }
            });

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    // Filament Helpers
    public function getTypeColor(): string
    {
        return match ($this->type) {
            TeamType::Enterprise => Color::Purple[500],
            TeamType::Organisation => Color::Blue[500],
            TeamType::Division => Color::Green[500],
            TeamType::Department => Color::Orange[500],
            TeamType::Project => Color::Gray[500],
        };
    }

    public function getTypeLabel(): string
    {
        return $this->type->getLabel();
    }

    public function getStatusColor(): string
    {
        return $this->latestStatus()?->color ?? Color::Gray[400];
    }

    public function getAvatarUrl(): ?string
    {
        return $this->executive?->avatar_url;
    }

    public function getFullSlugPath(): string
    {
        return $this->ancestorsAndSelf()
            ->orderBy('depth')
            ->pluck('slug')
            ->filter()
            ->implode('/');
    }

    public function getPublicUrl(): string
    {
        return route('teams.show', ['team' => $this->ulid]);
    }

    public function getSlugUrl(): string
    {
        return route('teams.show-by-slug', ['slug' => $this->slug]);
    }

    // Scopes
    public function scopeOfType($query, TeamType $type)
    {
        return $query->where('type', $type);
    }

    public function scopeRootLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeByUlid($query, string $ulid)
    {
        return $query->where('ulid', $ulid);
    }

    public function scopeBySlug($query, string $slug)
    {
        return $query->where('slug', $slug);
    }

    public function scopeInGraph($query, Team $rootTeam)
    {
        return $query->where(function ($query) use ($rootTeam) {
            $query->whereHas('ancestors', function ($ancestorQuery) use ($rootTeam) {
                $ancestorQuery->where('id', $rootTeam->id);
            })->orWhere('id', $rootTeam->id);
        });
    }
}
