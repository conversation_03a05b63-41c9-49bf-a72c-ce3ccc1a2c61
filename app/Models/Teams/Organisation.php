<?php

namespace App\Models\Teams;

use App\Enums\TeamType;
use App\Models\Team;
use Tightenco\Parental\HasParent;

class Organisation extends Team
{
    use HasParent;

    protected static function booted(): void
    {
        parent::booted();
        
        static::creating(function (Organisation $organisation) {
            $organisation->type = TeamType::Organisation;
        });
    }

    public function getAllowedParentTypes(): array
    {
        return [TeamType::Enterprise];
    }
}
