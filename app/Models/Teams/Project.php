<?php

namespace App\Models\Teams;

use App\Enums\TeamType;
use App\Models\Team;
use Tightenco\Parental\HasParent;

class Project extends Team
{
    use HasParent;

    protected static function booted(): void
    {
        parent::booted();
        
        static::creating(function (Project $project) {
            $project->type = TeamType::Project;
        });
    }

    public function getAllowedParentTypes(): array
    {
        return [
            TeamType::Enterprise,
            TeamType::Organisation,
            TeamType::Division,
            TeamType::Department,
        ];
    }

    public function canBeRoot(): bool
    {
        return false;
    }
}
