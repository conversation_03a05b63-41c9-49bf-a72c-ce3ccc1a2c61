<?php

namespace App\Models\Teams;

use App\Enums\TeamType;
use App\Models\Team;
use Tightenco\Parental\HasChildren;

class Enterprise extends Team
{
    use HasChildren;

    protected $childTypes = [
        'organisation' => Organisation::class,
    ];

    protected static function booted(): void
    {
        parent::booted();
        
        static::creating(function (Enterprise $enterprise) {
            $enterprise->type = TeamType::Enterprise;
            $enterprise->parent_id = null; // Enforce root constraint
        });
    }

    public function canHaveParent(): bool
    {
        return false;
    }
}
