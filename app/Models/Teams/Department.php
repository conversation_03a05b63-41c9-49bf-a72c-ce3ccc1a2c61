<?php

namespace App\Models\Teams;

use App\Enums\TeamType;
use App\Models\Team;
use Tightenco\Parental\HasParent;

class Department extends Team
{
    use HasParent;

    protected static function booted(): void
    {
        parent::booted();
        
        static::creating(function (Department $department) {
            $department->type = TeamType::Department;
        });
    }

    public function getAllowedParentTypes(): array
    {
        return [TeamType::Division, TeamType::Department];
    }
}
