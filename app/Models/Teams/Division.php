<?php

namespace App\Models\Teams;

use App\Enums\TeamType;
use App\Models\Team;
use Tightenco\Parental\HasParent;

class Division extends Team
{
    use HasParent;

    protected static function booted(): void
    {
        parent::booted();
        
        static::creating(function (Division $division) {
            $division->type = TeamType::Division;
        });
    }

    public function getAllowedParentTypes(): array
    {
        return [TeamType::Organisation, TeamType::Division];
    }
}
