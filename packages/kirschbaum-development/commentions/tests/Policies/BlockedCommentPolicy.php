<?php

namespace Tests\Policies;

use <PERSON><PERSON><PERSON>\Commentions\Comment;
use <PERSON><PERSON><PERSON>\Commentions\Contracts\Commenter;
use <PERSON><PERSON><PERSON>\Commentions\Policies\CommentPolicy;

class BlockedCommentPolicy extends CommentPolicy
{
    public function create(Commenter $user): bool
    {
        return false;
    }

    public function update($user, Comment $comment): bool
    {
        return false;
    }

    public function delete($user, Comment $comment): bool
    {
        return false;
    }
}
