{"preset": "laravel", "include": ["app/**/*.php", "bin/**/*.php", "bootstrap/**/*.php", "config/**/*.php", "database/**/*.php", "routes/**/*.php", "tests/**/*.php", "packages/**/src/**/*.php", "packages/**/tests/**/*.php", "plugins/**/*.php"], "exclude": ["vendor", "node_modules", "storage", "bootstrap/cache", "public", "database/migrations", "plugins/**/database/migrations", "reports/rector/cache"], "rules": {"declare_strict_types": true, "fully_qualified_strict_types": true, "native_function_type_declaration_casing": true, "no_unreachable_default_argument_value": true, "phpdoc_to_return_type": true, "return_type_declaration": true, "strict_comparison": true, "strict_param": true, "void_return": true, "array_push": true, "backtick_to_shell_exec": true, "date_time_immutable": true, "lowercase_keywords": true, "lowercase_static_reference": true, "final_class": false, "final_internal_class": false, "final_public_method_for_abstract_class": true, "global_namespace_import": {"import_classes": true, "import_constants": true, "import_functions": true}, "mb_str_functions": true, "modernize_types_casting": true, "new_with_parentheses": false, "no_multiple_statements_per_line": true, "ordered_interfaces": true, "ordered_traits": true, "protected_to_private": true, "self_accessor": true, "self_static_accessor": true, "visibility_required": true, "no_superfluous_elseif": true, "no_useless_else": true, "ordered_class_elements": {"order": ["use_trait", "case", "constant", "constant_public", "constant_protected", "constant_private", "property_public", "property_protected", "property_private", "construct", "destruct", "magic", "phpunit", "method_abstract", "method_public_static", "method_public", "method_protected_static", "method_protected", "method_private_static", "method_private"], "sort_algorithm": "none"}}}